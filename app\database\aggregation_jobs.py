#!/usr/bin/env python3
"""
Advanced TimescaleDB aggregation jobs for automated OHLCV data aggregation.
Implements FIRST/LAST functions for proper OHLC aggregation and scheduled policies.
"""

import logging
from sqlalchemy import create_engine, text
from sqlalchemy.exc import SQLAlchemyError

from app.core.config import get_database_settings
from app.core.logging import get_logger

logger = get_logger(__name__)


def create_aggregation_functions():
    """
    Create custom aggregation functions for OHLCV data.
    """
    database_settings = get_database_settings()
    engine = create_engine(database_settings.url)
    
    logger.info("Creating custom aggregation functions...")
    
    try:
        with engine.connect() as conn:
            # Create function for 15-minute aggregation
            conn.execute(text("""
                CREATE OR REPLACE FUNCTION aggregate_ohlcv_15min()
                RETURNS VOID AS $$
                BEGIN
                    -- Insert aggregated 15-minute data
                    INSERT INTO stock_ohlcv_15min (symbol, exchange, datetime, open, high, low, close, volume)
                    SELECT 
                        symbol,
                        exchange,
                        time_bucket('15 minutes', datetime) as bucket_time,
                        FIRST(open, datetime) as open,
                        MAX(high) as high,
                        MIN(low) as low,
                        LAST(close, datetime) as close,
                        SUM(volume) as volume
                    FROM stock_ohlcv
                    WHERE interval = '1m'
                        AND datetime >= NOW() - INTERVAL '1 day'
                        AND datetime < time_bucket('15 minutes', NOW())
                    GROUP BY symbol, exchange, bucket_time
                    ON CONFLICT (symbol, datetime) DO UPDATE SET
                        open = EXCLUDED.open,
                        high = EXCLUDED.high,
                        low = EXCLUDED.low,
                        close = EXCLUDED.close,
                        volume = EXCLUDED.volume;
                END;
                $$ LANGUAGE plpgsql;
            """))
            
            # Create function for 1-hour aggregation
            conn.execute(text("""
                CREATE OR REPLACE FUNCTION aggregate_ohlcv_1h()
                RETURNS VOID AS $$
                BEGIN
                    -- Insert aggregated 1-hour data
                    INSERT INTO stock_ohlcv_1h (symbol, exchange, datetime, open, high, low, close, volume)
                    SELECT 
                        symbol,
                        exchange,
                        time_bucket('1 hour', datetime) as bucket_time,
                        FIRST(open, datetime) as open,
                        MAX(high) as high,
                        MIN(low) as low,
                        LAST(close, datetime) as close,
                        SUM(volume) as volume
                    FROM stock_ohlcv
                    WHERE interval = '1m'
                        AND datetime >= NOW() - INTERVAL '2 days'
                        AND datetime < time_bucket('1 hour', NOW())
                    GROUP BY symbol, exchange, bucket_time
                    ON CONFLICT (symbol, datetime) DO UPDATE SET
                        open = EXCLUDED.open,
                        high = EXCLUDED.high,
                        low = EXCLUDED.low,
                        close = EXCLUDED.close,
                        volume = EXCLUDED.volume;
                END;
                $$ LANGUAGE plpgsql;
            """))
            
            # Create function for 1-day aggregation
            conn.execute(text("""
                CREATE OR REPLACE FUNCTION aggregate_ohlcv_1d()
                RETURNS VOID AS $$
                BEGIN
                    -- Insert aggregated 1-day data
                    INSERT INTO stock_ohlcv_1d (symbol, exchange, datetime, open, high, low, close, volume)
                    SELECT 
                        symbol,
                        exchange,
                        time_bucket('1 day', datetime) as bucket_time,
                        FIRST(open, datetime) as open,
                        MAX(high) as high,
                        MIN(low) as low,
                        LAST(close, datetime) as close,
                        SUM(volume) as volume
                    FROM stock_ohlcv
                    WHERE interval = '1m'
                        AND datetime >= NOW() - INTERVAL '7 days'
                        AND datetime < time_bucket('1 day', NOW())
                    GROUP BY symbol, exchange, bucket_time
                    ON CONFLICT (symbol, datetime) DO UPDATE SET
                        open = EXCLUDED.open,
                        high = EXCLUDED.high,
                        low = EXCLUDED.low,
                        close = EXCLUDED.close,
                        volume = EXCLUDED.volume;
                END;
                $$ LANGUAGE plpgsql;
            """))
            
            # Create comprehensive aggregation function
            conn.execute(text("""
                CREATE OR REPLACE FUNCTION run_all_aggregations()
                RETURNS VOID AS $$
                BEGIN
                    PERFORM aggregate_ohlcv_15min();
                    PERFORM aggregate_ohlcv_1h();
                    PERFORM aggregate_ohlcv_1d();
                    
                    -- Log completion
                    RAISE NOTICE 'All OHLCV aggregations completed at %', NOW();
                END;
                $$ LANGUAGE plpgsql;
            """))
            
            conn.commit()
            logger.info("✅ Custom aggregation functions created successfully")
            
    except Exception as e:
        logger.error(f"❌ Failed to create aggregation functions: {e}")
        raise


def setup_continuous_aggregates():
    """
    Setup TimescaleDB continuous aggregates for real-time aggregation.
    """
    database_settings = get_database_settings()
    engine = create_engine(database_settings.url)
    
    logger.info("Setting up continuous aggregates...")
    
    try:
        with engine.connect() as conn:
            # Create continuous aggregate for 15-minute data
            conn.execute(text("""
                CREATE MATERIALIZED VIEW IF NOT EXISTS stock_ohlcv_15min_cagg
                WITH (timescaledb.continuous) AS
                SELECT 
                    symbol,
                    exchange,
                    time_bucket('15 minutes', datetime) as bucket_time,
                    FIRST(open, datetime) as open,
                    MAX(high) as high,
                    MIN(low) as low,
                    LAST(close, datetime) as close,
                    SUM(volume) as volume,
                    COUNT(*) as tick_count
                FROM stock_ohlcv
                WHERE interval = '1m'
                GROUP BY symbol, exchange, bucket_time;
            """))
            
            # Create continuous aggregate for 1-hour data
            conn.execute(text("""
                CREATE MATERIALIZED VIEW IF NOT EXISTS stock_ohlcv_1h_cagg
                WITH (timescaledb.continuous) AS
                SELECT 
                    symbol,
                    exchange,
                    time_bucket('1 hour', datetime) as bucket_time,
                    FIRST(open, datetime) as open,
                    MAX(high) as high,
                    MIN(low) as low,
                    LAST(close, datetime) as close,
                    SUM(volume) as volume,
                    COUNT(*) as tick_count
                FROM stock_ohlcv
                WHERE interval = '1m'
                GROUP BY symbol, exchange, bucket_time;
            """))
            
            # Create continuous aggregate for 1-day data
            conn.execute(text("""
                CREATE MATERIALIZED VIEW IF NOT EXISTS stock_ohlcv_1d_cagg
                WITH (timescaledb.continuous) AS
                SELECT 
                    symbol,
                    exchange,
                    time_bucket('1 day', datetime) as bucket_time,
                    FIRST(open, datetime) as open,
                    MAX(high) as high,
                    MIN(low) as low,
                    LAST(close, datetime) as close,
                    SUM(volume) as volume,
                    COUNT(*) as tick_count
                FROM stock_ohlcv
                WHERE interval = '1m'
                GROUP BY symbol, exchange, bucket_time;
            """))
            
            conn.commit()
            logger.info("✅ Continuous aggregates created successfully")
            
    except Exception as e:
        logger.error(f"❌ Failed to create continuous aggregates: {e}")
        raise


def setup_aggregation_policies():
    """
    Setup automated aggregation policies for continuous aggregates.
    """
    database_settings = get_database_settings()
    engine = create_engine(database_settings.url)
    
    logger.info("Setting up aggregation policies...")
    
    try:
        with engine.connect() as conn:
            # Add refresh policy for 15-minute continuous aggregate
            conn.execute(text("""
                SELECT add_continuous_aggregate_policy('stock_ohlcv_15min_cagg',
                    start_offset => INTERVAL '1 hour',
                    end_offset => INTERVAL '15 minutes',
                    schedule_interval => INTERVAL '15 minutes');
            """))
            
            # Add refresh policy for 1-hour continuous aggregate
            conn.execute(text("""
                SELECT add_continuous_aggregate_policy('stock_ohlcv_1h_cagg',
                    start_offset => INTERVAL '4 hours',
                    end_offset => INTERVAL '1 hour',
                    schedule_interval => INTERVAL '1 hour');
            """))
            
            # Add refresh policy for 1-day continuous aggregate
            conn.execute(text("""
                SELECT add_continuous_aggregate_policy('stock_ohlcv_1d_cagg',
                    start_offset => INTERVAL '1 day',
                    end_offset => INTERVAL '1 hour',
                    schedule_interval => INTERVAL '1 hour');
            """))
            
            conn.commit()
            logger.info("✅ Aggregation policies setup successfully")
            
    except Exception as e:
        logger.error(f"❌ Failed to setup aggregation policies: {e}")
        raise


def create_monitoring_views():
    """
    Create monitoring views for tracking aggregation performance.
    """
    database_settings = get_database_settings()
    engine = create_engine(database_settings.url)
    
    logger.info("Creating monitoring views...")
    
    try:
        with engine.connect() as conn:
            # Create view for chunk monitoring
            conn.execute(text("""
                CREATE OR REPLACE VIEW chunk_monitoring AS
                SELECT 
                    hypertable_name,
                    chunk_name,
                    range_start,
                    range_end,
                    is_compressed,
                    compressed_chunk_id,
                    pg_size_pretty(total_bytes) as total_size,
                    pg_size_pretty(compressed_total_bytes) as compressed_size,
                    CASE 
                        WHEN total_bytes > 0 THEN 
                            ROUND((1 - compressed_total_bytes::float / total_bytes::float) * 100, 2)
                        ELSE 0 
                    END as compression_ratio
                FROM timescaledb_information.chunks
                WHERE hypertable_name IN ('stock_ohlcv', 'stock_ohlcv_15min', 'stock_ohlcv_1h', 'stock_ohlcv_1d')
                ORDER BY hypertable_name, range_start DESC;
            """))
            
            # Create view for aggregation job monitoring
            conn.execute(text("""
                CREATE OR REPLACE VIEW aggregation_job_stats AS
                SELECT 
                    job_id,
                    application_name,
                    schedule_interval,
                    max_runtime,
                    max_retries,
                    retry_period,
                    next_start,
                    last_finish,
                    last_successful_finish,
                    last_run_status,
                    total_runs,
                    total_successes,
                    total_failures
                FROM timescaledb_information.jobs
                WHERE application_name LIKE '%continuous_aggregate%'
                ORDER BY job_id;
            """))
            
            conn.commit()
            logger.info("✅ Monitoring views created successfully")
            
    except Exception as e:
        logger.error(f"❌ Failed to create monitoring views: {e}")
        raise


def initialize_aggregation_system():
    """
    Initialize the complete aggregation system.
    """
    logger.info("🚀 Initializing advanced aggregation system...")
    
    try:
        # Step 1: Create aggregation functions
        create_aggregation_functions()
        
        # Step 2: Setup continuous aggregates
        setup_continuous_aggregates()
        
        # Step 3: Setup aggregation policies
        setup_aggregation_policies()
        
        # Step 4: Create monitoring views
        create_monitoring_views()
        
        logger.info("🎉 Advanced aggregation system initialized successfully!")
        logger.info("📊 Real-time OHLCV aggregation is now active")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Failed to initialize aggregation system: {e}")
        return False


if __name__ == "__main__":
    success = initialize_aggregation_system()
    exit(0 if success else 1)
