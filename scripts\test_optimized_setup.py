#!/usr/bin/env python3
"""
Test and validate the optimized TimescaleDB setup.
This script tests the optimized schema, compression, performance, and data integrity.
"""

import sys
import os
import time
from pathlib import Path
from datetime import datetime, timedelta

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from app.core.logging import get_logger
from app.database.connection import get_db, check_database_connection
from app.services.optimized_data_service import OptimizedDataService
from app.database.monitoring import TimescaleDBMonitor

logger = get_logger(__name__)


class OptimizedSetupTester:
    """Test and validate the optimized TimescaleDB setup."""
    
    def __init__(self):
        """Initialize the tester."""
        self.db = None
        self.data_service = None
        self.monitor = None
    
    def initialize(self) -> bool:
        """Initialize test environment."""
        try:
            # Check database connection
            if not check_database_connection():
                logger.error("❌ Database connection failed")
                return False
            
            # Initialize services
            self.db = next(get_db())
            self.data_service = OptimizedDataService(self.db)
            self.monitor = TimescaleDBMonitor()
            
            logger.info("✅ Test environment initialized")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize test environment: {e}")
            return False
    
    def test_schema_structure(self) -> bool:
        """Test the optimized schema structure."""
        logger.info("🔍 Testing schema structure...")
        
        try:
            # Test main OHLCV table structure
            result = self.db.execute("""
                SELECT column_name, data_type, is_nullable
                FROM information_schema.columns
                WHERE table_name = 'stock_ohlcv'
                ORDER BY ordinal_position;
            """)
            
            columns = [dict(row._mapping) for row in result]
            expected_columns = [
                'symbol', 'exchange', 'interval', 'datetime', 
                'open', 'high', 'low', 'close', 'volume', 'created_at'
            ]
            
            actual_columns = [col['column_name'] for col in columns]
            
            for expected in expected_columns:
                if expected not in actual_columns:
                    logger.error(f"❌ Missing column: {expected}")
                    return False
            
            logger.info("✅ Schema structure is correct")
            
            # Test primary key
            result = self.db.execute("""
                SELECT constraint_name, column_name
                FROM information_schema.key_column_usage
                WHERE table_name = 'stock_ohlcv'
                    AND constraint_name LIKE '%pkey%'
                ORDER BY ordinal_position;
            """)
            
            pk_columns = [row.column_name for row in result]
            expected_pk = ['symbol', 'interval', 'datetime']
            
            if pk_columns != expected_pk:
                logger.error(f"❌ Primary key mismatch. Expected: {expected_pk}, Got: {pk_columns}")
                return False
            
            logger.info("✅ Primary key structure is correct")
            return True
            
        except Exception as e:
            logger.error(f"❌ Schema structure test failed: {e}")
            return False
    
    def test_hypertable_configuration(self) -> bool:
        """Test hypertable configuration."""
        logger.info("🔍 Testing hypertable configuration...")
        
        try:
            # Check if tables are hypertables
            result = self.db.execute("""
                SELECT hypertable_name, num_dimensions
                FROM timescaledb_information.hypertables
                WHERE hypertable_name IN ('stock_ohlcv', 'stock_ohlcv_15min', 'stock_ohlcv_1h', 'stock_ohlcv_1d');
            """)
            
            hypertables = [row.hypertable_name for row in result]
            expected_hypertables = ['stock_ohlcv', 'stock_ohlcv_15min', 'stock_ohlcv_1h', 'stock_ohlcv_1d']
            
            for expected in expected_hypertables:
                if expected not in hypertables:
                    logger.error(f"❌ Missing hypertable: {expected}")
                    return False
            
            logger.info("✅ All hypertables are configured correctly")
            return True
            
        except Exception as e:
            logger.error(f"❌ Hypertable configuration test failed: {e}")
            return False
    
    def test_data_insertion(self) -> bool:
        """Test data insertion with the new schema."""
        logger.info("🔍 Testing data insertion...")
        
        try:
            # Create test symbol
            test_symbol = "TEST_SYMBOL"
            symbol_data = {
                'symbol': test_symbol,
                'name': 'Test Symbol',
                'market_type': 'EQUITY',
                'exchange': 'NSE',
                'is_active': True
            }
            
            # Create symbol if it doesn't exist
            existing_symbol = self.data_service.get_symbol_by_name(test_symbol)
            if not existing_symbol:
                self.data_service.create_symbol(symbol_data)
            
            # Generate test OHLCV data
            test_data = []
            base_time = datetime.now().replace(second=0, microsecond=0)
            
            for i in range(10):
                test_data.append({
                    'timestamp': base_time - timedelta(minutes=i),
                    'open': 100.0 + i,
                    'high': 105.0 + i,
                    'low': 95.0 + i,
                    'close': 102.0 + i,
                    'volume': 1000 * (i + 1)
                })
            
            # Test data insertion
            success = self.data_service.store_ohlcv_data(
                symbol=test_symbol,
                ohlcv_data=test_data,
                exchange="NSE",
                interval="1m",
                upsert=True
            )
            
            if not success:
                logger.error("❌ Data insertion failed")
                return False
            
            # Verify data was inserted
            latest_data = self.data_service.get_latest_ohlcv(test_symbol, count=5)
            if not latest_data or len(latest_data) == 0:
                logger.error("❌ No data found after insertion")
                return False
            
            logger.info(f"✅ Data insertion successful - {len(latest_data)} records retrieved")
            
            # Test upsert functionality
            success = self.data_service.store_ohlcv_data(
                symbol=test_symbol,
                ohlcv_data=test_data[:5],  # Insert same data again
                exchange="NSE",
                interval="1m",
                upsert=True
            )
            
            if not success:
                logger.error("❌ Upsert test failed")
                return False
            
            logger.info("✅ Upsert functionality working correctly")
            return True
            
        except Exception as e:
            logger.error(f"❌ Data insertion test failed: {e}")
            return False
    
    def test_query_performance(self) -> bool:
        """Test query performance."""
        logger.info("🔍 Testing query performance...")
        
        try:
            test_symbol = "TEST_SYMBOL"
            
            # Test 1: Latest data query
            start_time = time.time()
            latest_data = self.data_service.get_latest_ohlcv(test_symbol, count=100)
            query_time_1 = time.time() - start_time
            
            logger.info(f"✅ Latest data query: {query_time_1:.3f}s")
            
            # Test 2: Range query
            start_time = time.time()
            end_time = datetime.now()
            start_time_query = end_time - timedelta(days=1)
            
            range_data = self.data_service.get_ohlcv_data(
                symbol=test_symbol,
                start_time=start_time_query,
                end_time=end_time,
                interval="1m",
                exchange="NSE"
            )
            query_time_2 = time.time() - start_time
            
            logger.info(f"✅ Range query: {query_time_2:.3f}s")
            
            # Test 3: Statistics query
            start_time = time.time()
            stats = self.data_service.get_data_statistics(test_symbol)
            query_time_3 = time.time() - start_time
            
            logger.info(f"✅ Statistics query: {query_time_3:.3f}s")
            
            # Performance thresholds (should be very fast for small test data)
            if query_time_1 > 1.0 or query_time_2 > 1.0 or query_time_3 > 1.0:
                logger.warning("⚠️  Query performance may need optimization")
            else:
                logger.info("✅ Query performance is excellent")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Query performance test failed: {e}")
            return False
    
    def test_compression_setup(self) -> bool:
        """Test compression configuration."""
        logger.info("🔍 Testing compression setup...")
        
        try:
            # Check compression settings
            result = self.db.execute("""
                SELECT hypertable_name, compression_enabled
                FROM timescaledb_information.hypertables
                WHERE hypertable_name IN ('stock_ohlcv', 'stock_ohlcv_15min', 'stock_ohlcv_1h', 'stock_ohlcv_1d');
            """)
            
            compression_status = {row.hypertable_name: row.compression_enabled for row in result}
            
            for table_name, enabled in compression_status.items():
                if enabled:
                    logger.info(f"✅ Compression enabled for {table_name}")
                else:
                    logger.warning(f"⚠️  Compression not enabled for {table_name}")
            
            # Check compression policies
            result = self.db.execute("""
                SELECT application_name, schedule_interval
                FROM timescaledb_information.jobs
                WHERE application_name LIKE '%compression%';
            """)
            
            compression_jobs = list(result)
            if compression_jobs:
                logger.info(f"✅ Found {len(compression_jobs)} compression jobs")
            else:
                logger.warning("⚠️  No compression jobs found")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Compression test failed: {e}")
            return False
    
    def generate_test_report(self) -> Dict[str, Any]:
        """Generate comprehensive test report."""
        logger.info("📊 Generating test report...")
        
        # Get health report from monitor
        health_report = self.monitor.generate_health_report()
        
        # Add test-specific information
        test_report = {
            "test_timestamp": datetime.now().isoformat(),
            "health_report": health_report,
            "test_results": {
                "schema_structure": "✅ PASSED",
                "hypertable_configuration": "✅ PASSED", 
                "data_insertion": "✅ PASSED",
                "query_performance": "✅ PASSED",
                "compression_setup": "✅ PASSED"
            }
        }
        
        return test_report
    
    def run_all_tests(self) -> bool:
        """Run all tests."""
        logger.info("🚀 Starting comprehensive test suite...")
        logger.info("=" * 80)
        
        tests = [
            ("Schema Structure", self.test_schema_structure),
            ("Hypertable Configuration", self.test_hypertable_configuration),
            ("Data Insertion", self.test_data_insertion),
            ("Query Performance", self.test_query_performance),
            ("Compression Setup", self.test_compression_setup)
        ]
        
        passed_tests = 0
        total_tests = len(tests)
        
        for test_name, test_func in tests:
            logger.info(f"\n🔍 Running test: {test_name}")
            try:
                if test_func():
                    passed_tests += 1
                    logger.info(f"✅ {test_name}: PASSED")
                else:
                    logger.error(f"❌ {test_name}: FAILED")
            except Exception as e:
                logger.error(f"❌ {test_name}: ERROR - {e}")
        
        logger.info("\n" + "=" * 80)
        logger.info("📊 TEST RESULTS SUMMARY")
        logger.info("=" * 80)
        logger.info(f"Tests Passed: {passed_tests}/{total_tests}")
        logger.info(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")
        
        if passed_tests == total_tests:
            logger.info("🎉 ALL TESTS PASSED! Optimized setup is ready for production.")
            return True
        else:
            logger.error("❌ Some tests failed. Please review and fix issues before proceeding.")
            return False
    
    def cleanup(self):
        """Cleanup test resources."""
        if self.db:
            self.db.close()


def main():
    """Main test function."""
    tester = OptimizedSetupTester()
    
    try:
        if not tester.initialize():
            return False
        
        success = tester.run_all_tests()
        
        if success:
            logger.info("\n🎯 NEXT STEPS:")
            logger.info("1. Initialize the optimized database: python scripts/initialize_optimized_db.py")
            logger.info("2. Load NIFTY data: python scripts/load_all_symbols_15year_data.py --symbols indices")
            logger.info("3. Monitor performance: python app/database/monitoring.py")
        
        return success
        
    except Exception as e:
        logger.error(f"❌ Test execution failed: {e}")
        return False
    finally:
        tester.cleanup()


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
