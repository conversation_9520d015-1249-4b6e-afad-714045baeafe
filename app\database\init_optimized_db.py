#!/usr/bin/env python3
"""
Optimized TimescaleDB initialization script for high-performance time-series data.
Implements all missing optimizations for handling 2500+ symbols and 15+ years of data.
"""

import logging
from sqlalchemy import create_engine, text
from sqlalchemy.exc import SQLAlchemyError

from app.core.config import get_database_settings
from app.core.logging import get_logger

logger = get_logger(__name__)

def create_optimized_hypertables():
    """
    Create optimized hypertables with proper configuration for large-scale data.
    Implements 30-day chunks and space partitioning for optimal performance.
    """
    database_settings = get_database_settings()
    engine = create_engine(database_settings.url)
    
    logger.info("Creating optimized TimescaleDB hypertables...")
    
    try:
        with engine.connect() as conn:
            # Drop existing hypertables if they exist (for migration)
            logger.info("Dropping existing hypertables for migration...")
            
            # Check if tables exist and drop them
            existing_tables = [
                'stock_ohlcv', 'stock_ohlcv_agg', 'stock_ohlcv_15min', 
                'stock_ohlcv_1h', 'stock_ohlcv_1d'
            ]
            
            for table in existing_tables:
                try:
                    # Check if table exists
                    result = conn.execute(text(f"""
                        SELECT EXISTS (
                            SELECT FROM information_schema.tables 
                            WHERE table_name = '{table}'
                        );
                    """))
                    
                    if result.scalar():
                        logger.info(f"Dropping existing table: {table}")
                        conn.execute(text(f"DROP TABLE IF EXISTS {table} CASCADE;"))
                except Exception as e:
                    logger.warning(f"Could not drop table {table}: {e}")
            
            conn.commit()
            
            # Create optimized main OHLCV table with composite primary key
            logger.info("Creating optimized stock_ohlcv hypertable...")
            conn.execute(text("""
                CREATE TABLE stock_ohlcv (
                    symbol TEXT NOT NULL,
                    exchange TEXT NOT NULL DEFAULT 'NSE',
                    interval TEXT NOT NULL DEFAULT '1m',
                    datetime TIMESTAMPTZ NOT NULL,
                    open FLOAT NOT NULL,
                    high FLOAT NOT NULL,
                    low FLOAT NOT NULL,
                    close FLOAT NOT NULL,
                    volume BIGINT NOT NULL,
                    created_at TIMESTAMPTZ DEFAULT NOW(),
                    PRIMARY KEY (symbol, interval, datetime)
                );
            """))
            
            # Convert to hypertable with optimized configuration
            conn.execute(text("""
                SELECT create_hypertable(
                    'stock_ohlcv', 
                    'datetime',
                    partitioning_column => 'symbol',
                    number_partitions => 20,
                    chunk_time_interval => INTERVAL '30 days',
                    if_not_exists => TRUE
                );
            """))
            
            # Create separate timeframe tables with optimized configuration
            timeframe_tables = [
                ('stock_ohlcv_15min', '15 days'),
                ('stock_ohlcv_1h', '30 days'),
                ('stock_ohlcv_1d', '90 days')
            ]
            
            for table_name, chunk_interval in timeframe_tables:
                logger.info(f"Creating {table_name} hypertable...")
                conn.execute(text(f"""
                    CREATE TABLE {table_name} (
                        symbol TEXT NOT NULL,
                        exchange TEXT NOT NULL DEFAULT 'NSE',
                        datetime TIMESTAMPTZ NOT NULL,
                        open FLOAT NOT NULL,
                        high FLOAT NOT NULL,
                        low FLOAT NOT NULL,
                        close FLOAT NOT NULL,
                        volume BIGINT NOT NULL,
                        created_at TIMESTAMPTZ DEFAULT NOW(),
                        PRIMARY KEY (symbol, datetime)
                    );
                """))
                
                # Convert to hypertable with space partitioning
                conn.execute(text(f"""
                    SELECT create_hypertable(
                        '{table_name}', 
                        'datetime',
                        partitioning_column => 'symbol',
                        number_partitions => 10,
                        chunk_time_interval => INTERVAL '{chunk_interval}',
                        if_not_exists => TRUE
                    );
                """))
            
            # Keep legacy aggregated table for backward compatibility
            logger.info("Creating legacy stock_ohlcv_agg table...")
            conn.execute(text("""
                CREATE TABLE stock_ohlcv_agg (
                    symbol_id INTEGER NOT NULL,
                    timeframe VARCHAR(10) NOT NULL,
                    timestamp TIMESTAMPTZ NOT NULL,
                    open DECIMAL(12,4) NOT NULL,
                    high DECIMAL(12,4) NOT NULL,
                    low DECIMAL(12,4) NOT NULL,
                    close DECIMAL(12,4) NOT NULL,
                    volume BIGINT NOT NULL,
                    created_at TIMESTAMPTZ DEFAULT NOW(),
                    PRIMARY KEY (symbol_id, timeframe, timestamp)
                );
            """))
            
            conn.execute(text("""
                SELECT create_hypertable(
                    'stock_ohlcv_agg', 
                    'timestamp',
                    chunk_time_interval => INTERVAL '7 days',
                    if_not_exists => TRUE
                );
            """))
            
            conn.commit()
            logger.info("✅ Optimized hypertables created successfully")
            
    except Exception as e:
        logger.error(f"❌ Failed to create optimized hypertables: {e}")
        raise


def create_optimized_indexes():
    """
    Create optimized indexes for high-performance queries.
    """
    database_settings = get_database_settings()
    engine = create_engine(database_settings.url)
    
    logger.info("Creating optimized indexes...")
    
    try:
        with engine.connect() as conn:
            # Optimized indexes for main OHLCV table
            indexes = [
                # Primary composite indexes
                "CREATE INDEX IF NOT EXISTS idx_stock_ohlcv_symbol_interval_datetime ON stock_ohlcv (symbol, interval, datetime DESC);",
                "CREATE INDEX IF NOT EXISTS idx_stock_ohlcv_symbol_datetime ON stock_ohlcv (symbol, datetime DESC);",
                "CREATE INDEX IF NOT EXISTS idx_stock_ohlcv_exchange_datetime ON stock_ohlcv (exchange, datetime DESC);",
                
                # Volume-based indexes for screening
                "CREATE INDEX IF NOT EXISTS idx_stock_ohlcv_volume_datetime ON stock_ohlcv (volume DESC, datetime DESC);",
                "CREATE INDEX IF NOT EXISTS idx_stock_ohlcv_symbol_volume ON stock_ohlcv (symbol, volume DESC);",
                
                # Interval-specific indexes
                "CREATE INDEX IF NOT EXISTS idx_stock_ohlcv_interval_datetime ON stock_ohlcv (interval, datetime DESC);",
                
                # Timeframe table indexes
                "CREATE INDEX IF NOT EXISTS idx_stock_ohlcv_15min_symbol_datetime ON stock_ohlcv_15min (symbol, datetime DESC);",
                "CREATE INDEX IF NOT EXISTS idx_stock_ohlcv_1h_symbol_datetime ON stock_ohlcv_1h (symbol, datetime DESC);",
                "CREATE INDEX IF NOT EXISTS idx_stock_ohlcv_1d_symbol_datetime ON stock_ohlcv_1d (symbol, datetime DESC);",
                
                # Exchange-based indexes for timeframe tables
                "CREATE INDEX IF NOT EXISTS idx_stock_ohlcv_15min_exchange_datetime ON stock_ohlcv_15min (exchange, datetime DESC);",
                "CREATE INDEX IF NOT EXISTS idx_stock_ohlcv_1h_exchange_datetime ON stock_ohlcv_1h (exchange, datetime DESC);",
                "CREATE INDEX IF NOT EXISTS idx_stock_ohlcv_1d_exchange_datetime ON stock_ohlcv_1d (exchange, datetime DESC);",
            ]
            
            for index_sql in indexes:
                try:
                    conn.execute(text(index_sql))
                    logger.info(f"✅ Created index: {index_sql.split('idx_')[1].split(' ')[0]}")
                except Exception as e:
                    logger.warning(f"⚠️  Index creation warning: {e}")
            
            conn.commit()
            logger.info("✅ Optimized indexes created successfully")
            
    except Exception as e:
        logger.error(f"❌ Failed to create optimized indexes: {e}")
        raise


def setup_compression_policies():
    """
    Setup TimescaleDB compression policies for automatic compression.
    """
    database_settings = get_database_settings()
    engine = create_engine(database_settings.url)
    
    logger.info("Setting up TimescaleDB compression policies...")
    
    try:
        with engine.connect() as conn:
            # Enable compression on main OHLCV table
            logger.info("Enabling compression on stock_ohlcv...")
            conn.execute(text("""
                ALTER TABLE stock_ohlcv SET (
                    timescaledb.compress,
                    timescaledb.compress_segmentby = 'symbol, exchange, interval',
                    timescaledb.compress_orderby = 'datetime DESC'
                );
            """))
            
            # Add compression policy - compress data older than 7 days
            conn.execute(text("""
                SELECT add_compression_policy('stock_ohlcv', INTERVAL '7 days');
            """))
            
            # Enable compression on timeframe tables
            timeframe_tables = ['stock_ohlcv_15min', 'stock_ohlcv_1h', 'stock_ohlcv_1d']
            compression_intervals = ['3 days', '7 days', '30 days']
            
            for table, interval in zip(timeframe_tables, compression_intervals):
                logger.info(f"Enabling compression on {table}...")
                conn.execute(text(f"""
                    ALTER TABLE {table} SET (
                        timescaledb.compress,
                        timescaledb.compress_segmentby = 'symbol, exchange',
                        timescaledb.compress_orderby = 'datetime DESC'
                    );
                """))
                
                conn.execute(text(f"""
                    SELECT add_compression_policy('{table}', INTERVAL '{interval}');
                """))
            
            conn.commit()
            logger.info("✅ Compression policies setup successfully")
            
    except Exception as e:
        logger.error(f"❌ Failed to setup compression policies: {e}")
        raise


def setup_retention_policies():
    """
    Setup data retention policies for automatic cleanup.
    """
    database_settings = get_database_settings()
    engine = create_engine(database_settings.url)
    
    logger.info("Setting up data retention policies...")
    
    try:
        with engine.connect() as conn:
            # Retention policy for 1-minute data (keep 5 years)
            conn.execute(text("""
                SELECT add_retention_policy('stock_ohlcv', INTERVAL '5 years');
            """))
            
            # Retention policies for aggregated data (keep longer)
            retention_policies = [
                ('stock_ohlcv_15min', '10 years'),
                ('stock_ohlcv_1h', '15 years'),
                ('stock_ohlcv_1d', '20 years')
            ]
            
            for table, retention in retention_policies:
                conn.execute(text(f"""
                    SELECT add_retention_policy('{table}', INTERVAL '{retention}');
                """))
                logger.info(f"✅ Set retention policy for {table}: {retention}")
            
            conn.commit()
            logger.info("✅ Retention policies setup successfully")
            
    except Exception as e:
        logger.error(f"❌ Failed to setup retention policies: {e}")
        raise


def initialize_optimized_database():
    """
    Initialize the complete optimized TimescaleDB setup.
    """
    logger.info("🚀 Initializing optimized TimescaleDB setup...")
    
    try:
        # Step 1: Create optimized hypertables
        create_optimized_hypertables()
        
        # Step 2: Create optimized indexes
        create_optimized_indexes()
        
        # Step 3: Setup compression policies
        setup_compression_policies()
        
        # Step 4: Setup retention policies
        setup_retention_policies()
        
        logger.info("🎉 Optimized TimescaleDB setup completed successfully!")
        logger.info("📊 Ready for high-performance time-series data storage")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Failed to initialize optimized database: {e}")
        return False


if __name__ == "__main__":
    success = initialize_optimized_database()
    exit(0 if success else 1)
