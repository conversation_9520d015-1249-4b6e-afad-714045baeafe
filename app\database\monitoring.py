#!/usr/bin/env python3
"""
TimescaleDB monitoring and maintenance system.
Provides comprehensive monitoring, performance tracking, and automated maintenance.
"""

import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any
from sqlalchemy import create_engine, text
from sqlalchemy.exc import SQLAlchemyError

from app.core.config import get_database_settings
from app.core.logging import get_logger

logger = get_logger(__name__)


class TimescaleDBMonitor:
    """Comprehensive TimescaleDB monitoring and maintenance system."""
    
    def __init__(self):
        """Initialize the monitor."""
        self.database_settings = get_database_settings()
        self.engine = create_engine(self.database_settings.url)
    
    def get_chunk_statistics(self) -> List[Dict[str, Any]]:
        """Get detailed chunk statistics for all hypertables."""
        try:
            with self.engine.connect() as conn:
                result = conn.execute(text("""
                    SELECT 
                        hypertable_name,
                        COUNT(*) as total_chunks,
                        COUNT(CASE WHEN is_compressed THEN 1 END) as compressed_chunks,
                        SUM(total_bytes) as total_bytes,
                        SUM(compressed_total_bytes) as compressed_bytes,
                        AVG(CASE 
                            WHEN total_bytes > 0 THEN 
                                (1 - compressed_total_bytes::float / total_bytes::float) * 100
                            ELSE 0 
                        END) as avg_compression_ratio,
                        MIN(range_start) as earliest_data,
                        MAX(range_end) as latest_data
                    FROM timescaledb_information.chunks
                    WHERE hypertable_name IN ('stock_ohlcv', 'stock_ohlcv_15min', 'stock_ohlcv_1h', 'stock_ohlcv_1d')
                    GROUP BY hypertable_name
                    ORDER BY hypertable_name;
                """))
                
                return [dict(row._mapping) for row in result]
                
        except Exception as e:
            logger.error(f"Failed to get chunk statistics: {e}")
            return []
    
    def get_compression_status(self) -> List[Dict[str, Any]]:
        """Get compression status for all chunks."""
        try:
            with self.engine.connect() as conn:
                result = conn.execute(text("""
                    SELECT 
                        hypertable_name,
                        chunk_name,
                        range_start,
                        range_end,
                        is_compressed,
                        pg_size_pretty(total_bytes) as uncompressed_size,
                        pg_size_pretty(compressed_total_bytes) as compressed_size,
                        CASE 
                            WHEN total_bytes > 0 THEN 
                                ROUND((1 - compressed_total_bytes::float / total_bytes::float) * 100, 2)
                            ELSE 0 
                        END as compression_ratio
                    FROM timescaledb_information.chunks
                    WHERE hypertable_name IN ('stock_ohlcv', 'stock_ohlcv_15min', 'stock_ohlcv_1h', 'stock_ohlcv_1d')
                        AND range_end < NOW() - INTERVAL '1 day'
                    ORDER BY hypertable_name, range_start DESC
                    LIMIT 50;
                """))
                
                return [dict(row._mapping) for row in result]
                
        except Exception as e:
            logger.error(f"Failed to get compression status: {e}")
            return []
    
    def get_job_statistics(self) -> List[Dict[str, Any]]:
        """Get statistics for background jobs."""
        try:
            with self.engine.connect() as conn:
                result = conn.execute(text("""
                    SELECT 
                        job_id,
                        application_name,
                        schedule_interval,
                        next_start,
                        last_finish,
                        last_successful_finish,
                        last_run_status,
                        total_runs,
                        total_successes,
                        total_failures,
                        CASE 
                            WHEN total_runs > 0 THEN 
                                ROUND((total_successes::float / total_runs::float) * 100, 2)
                            ELSE 0 
                        END as success_rate
                    FROM timescaledb_information.jobs
                    ORDER BY job_id;
                """))
                
                return [dict(row._mapping) for row in result]
                
        except Exception as e:
            logger.error(f"Failed to get job statistics: {e}")
            return []
    
    def get_table_sizes(self) -> List[Dict[str, Any]]:
        """Get size information for all tables."""
        try:
            with self.engine.connect() as conn:
                result = conn.execute(text("""
                    SELECT 
                        schemaname,
                        tablename,
                        pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as total_size,
                        pg_size_pretty(pg_relation_size(schemaname||'.'||tablename)) as table_size,
                        pg_size_pretty(pg_indexes_size(schemaname||'.'||tablename)) as index_size
                    FROM pg_tables 
                    WHERE tablename IN ('stock_ohlcv', 'stock_ohlcv_15min', 'stock_ohlcv_1h', 'stock_ohlcv_1d', 'symbols')
                        AND schemaname = 'public'
                    ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;
                """))
                
                return [dict(row._mapping) for row in result]
                
        except Exception as e:
            logger.error(f"Failed to get table sizes: {e}")
            return []
    
    def get_query_performance(self) -> List[Dict[str, Any]]:
        """Get query performance statistics."""
        try:
            with self.engine.connect() as conn:
                result = conn.execute(text("""
                    SELECT 
                        query,
                        calls,
                        total_time,
                        mean_time,
                        min_time,
                        max_time,
                        stddev_time,
                        rows
                    FROM pg_stat_statements 
                    WHERE query LIKE '%stock_ohlcv%'
                        AND calls > 10
                    ORDER BY total_time DESC
                    LIMIT 20;
                """))
                
                return [dict(row._mapping) for row in result]
                
        except Exception as e:
            logger.warning(f"Query performance stats not available (pg_stat_statements not enabled): {e}")
            return []
    
    def compress_eligible_chunks(self) -> int:
        """Manually compress chunks that are eligible for compression."""
        try:
            compressed_count = 0
            
            with self.engine.connect() as conn:
                # Get chunks eligible for compression (older than 7 days and not compressed)
                result = conn.execute(text("""
                    SELECT hypertable_name, chunk_name
                    FROM timescaledb_information.chunks
                    WHERE hypertable_name IN ('stock_ohlcv', 'stock_ohlcv_15min', 'stock_ohlcv_1h', 'stock_ohlcv_1d')
                        AND is_compressed = FALSE
                        AND range_end < NOW() - INTERVAL '7 days'
                    ORDER BY range_end
                    LIMIT 10;
                """))
                
                chunks = result.fetchall()
                
                for chunk in chunks:
                    try:
                        conn.execute(text(f"SELECT compress_chunk('{chunk.chunk_name}');"))
                        compressed_count += 1
                        logger.info(f"✅ Compressed chunk: {chunk.chunk_name}")
                    except Exception as e:
                        logger.warning(f"⚠️  Failed to compress chunk {chunk.chunk_name}: {e}")
                
                conn.commit()
            
            return compressed_count
            
        except Exception as e:
            logger.error(f"Failed to compress chunks: {e}")
            return 0
    
    def vacuum_and_analyze(self) -> bool:
        """Run VACUUM and ANALYZE on all tables."""
        try:
            tables = ['stock_ohlcv', 'stock_ohlcv_15min', 'stock_ohlcv_1h', 'stock_ohlcv_1d', 'symbols']
            
            with self.engine.connect() as conn:
                for table in tables:
                    try:
                        # Use autocommit for VACUUM
                        conn.execute(text(f"VACUUM ANALYZE {table};"))
                        logger.info(f"✅ VACUUM ANALYZE completed for {table}")
                    except Exception as e:
                        logger.warning(f"⚠️  VACUUM ANALYZE failed for {table}: {e}")
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to run VACUUM ANALYZE: {e}")
            return False
    
    def cleanup_old_chunks(self, retention_days: int = 1825) -> int:  # 5 years default
        """Remove chunks older than retention period."""
        try:
            removed_count = 0
            cutoff_date = datetime.now() - timedelta(days=retention_days)
            
            with self.engine.connect() as conn:
                # Get old chunks
                result = conn.execute(text("""
                    SELECT chunk_name
                    FROM timescaledb_information.chunks
                    WHERE hypertable_name = 'stock_ohlcv'
                        AND range_end < :cutoff_date
                    ORDER BY range_end
                    LIMIT 5;
                """), {"cutoff_date": cutoff_date})
                
                chunks = result.fetchall()
                
                for chunk in chunks:
                    try:
                        conn.execute(text(f"SELECT drop_chunk('{chunk.chunk_name}');"))
                        removed_count += 1
                        logger.info(f"✅ Removed old chunk: {chunk.chunk_name}")
                    except Exception as e:
                        logger.warning(f"⚠️  Failed to remove chunk {chunk.chunk_name}: {e}")
                
                conn.commit()
            
            return removed_count
            
        except Exception as e:
            logger.error(f"Failed to cleanup old chunks: {e}")
            return 0
    
    def generate_health_report(self) -> Dict[str, Any]:
        """Generate comprehensive health report."""
        logger.info("📊 Generating TimescaleDB health report...")
        
        report = {
            "timestamp": datetime.now().isoformat(),
            "chunk_statistics": self.get_chunk_statistics(),
            "compression_status": self.get_compression_status(),
            "job_statistics": self.get_job_statistics(),
            "table_sizes": self.get_table_sizes(),
            "query_performance": self.get_query_performance()
        }
        
        return report
    
    def run_maintenance(self) -> Dict[str, Any]:
        """Run comprehensive maintenance tasks."""
        logger.info("🔧 Running TimescaleDB maintenance...")
        
        maintenance_results = {
            "timestamp": datetime.now().isoformat(),
            "compressed_chunks": self.compress_eligible_chunks(),
            "vacuum_analyze_success": self.vacuum_and_analyze(),
            "removed_chunks": self.cleanup_old_chunks()
        }
        
        logger.info(f"✅ Maintenance completed: {maintenance_results}")
        return maintenance_results


def print_health_report():
    """Print a formatted health report."""
    monitor = TimescaleDBMonitor()
    report = monitor.generate_health_report()
    
    print("\n" + "="*80)
    print("📊 TIMESCALEDB HEALTH REPORT")
    print("="*80)
    print(f"Generated: {report['timestamp']}")
    
    print("\n📦 CHUNK STATISTICS:")
    print("-" * 60)
    for stat in report['chunk_statistics']:
        print(f"Table: {stat['hypertable_name']}")
        print(f"  Total Chunks: {stat['total_chunks']}")
        print(f"  Compressed: {stat['compressed_chunks']}")
        print(f"  Compression Ratio: {stat['avg_compression_ratio']:.2f}%")
        print(f"  Data Range: {stat['earliest_data']} to {stat['latest_data']}")
        print()
    
    print("\n💾 TABLE SIZES:")
    print("-" * 60)
    for size in report['table_sizes']:
        print(f"{size['tablename']}: {size['total_size']} (Table: {size['table_size']}, Indexes: {size['index_size']})")
    
    print("\n⚙️  BACKGROUND JOBS:")
    print("-" * 60)
    for job in report['job_statistics']:
        print(f"Job {job['job_id']}: {job['application_name']}")
        print(f"  Success Rate: {job['success_rate']:.2f}%")
        print(f"  Last Status: {job['last_run_status']}")
        print()
    
    print("="*80)


def run_maintenance():
    """Run maintenance tasks."""
    monitor = TimescaleDBMonitor()
    results = monitor.run_maintenance()
    
    print("\n" + "="*80)
    print("🔧 MAINTENANCE RESULTS")
    print("="*80)
    print(f"Compressed Chunks: {results['compressed_chunks']}")
    print(f"VACUUM ANALYZE: {'✅ Success' if results['vacuum_analyze_success'] else '❌ Failed'}")
    print(f"Removed Old Chunks: {results['removed_chunks']}")
    print("="*80)


if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "maintenance":
        run_maintenance()
    else:
        print_health_report()
